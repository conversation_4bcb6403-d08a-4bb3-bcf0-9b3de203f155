const { Pool } = require('pg');

const pool = new Pool({
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_DATABASE,
    ssl: {
        rejectUnauthorized: false
    }
});

// Helper to add CORS headers to every response
function withCors(response) {
    return {
        ...response,
        headers: {
            'Access-Control-Allow-Origin': '*',
            // add these if you want to support credentials or extra headers:
            // 'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,OPTIONS,DELETE',
            ...response.headers,
        }
    };
}

exports.handler = async (event) => {
    console.log("Event received:", JSON.stringify(event));

    try {
        if (event.httpMethod === 'OPTIONS') {
            return withCors({
                statusCode: 200,
                body: '',
              });
        }

        // Check for API key
        const apiKey = event.requestContext?.identity?.apiKey;
        if (!apiKey || apiKey !== process.env.API_KEY) {
            return withCors({
                statusCode: 401,
                body: JSON.stringify({
                    error: 'Unauthorized: Invalid API key',
                    providedApiKey: apiKey || null
                })
            });
        }

        switch (event.httpMethod) {
            case 'GET':
                return withCors(await handleGet(event));
            case 'POST':
                return withCors(await handlePost(event));
            case 'PUT':
                return withCors(await handlePut(event));
            case 'DELETE':
                return withCors(await handleDelete(event));
            default:
                return withCors({
                    statusCode: 405,
                    body: JSON.stringify({
                        error: 'Method not allowed. This is what was received:',
                        receivedMethod: event.httpMethod,
                        receivedPath: event.path
                    })
                });
        }
    } catch (error) {
        console.error('Error stack:', error.stack);
        console.error('Error message:', error.message);
        return withCors({
            statusCode: 500,
            body: JSON.stringify({ error: 'Internal server error', message: error.message })
        });
    }
};

async function handleGet(event) {
    let { pilot, drone_name, datetime, page, archives, property_name, id } = event.queryStringParameters || {};

    console.log("Received pilot:", pilot);
    console.log("Received drone_name:", drone_name);
    console.log("Received datetime:", datetime);
    console.log("Received page:", page);
    console.log("Received archives:", archives);
    console.log("Received property_name:", property_name);
    console.log("Received id:", id);

    // Handle archive retrieval
    if (archives === 'true') {
        return await handleGetArchives(event);
    }

    // Handle single survey retrieval by ID
    if (id) {
        return await handleGetSurveyById(id);
    }

    if (!pilot || !drone_name || !datetime) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required parameters: pilot, drone_name, and datetime are required'
            })
        };
    }

    const PAGE_SIZE = 3;
    let getQuery;
    let params;

    if (pilot === "Master" && drone_name === "Master") {
        const pageNumber = parseInt(page, 10) || 1;
        const offset = (pageNumber - 1) * PAGE_SIZE;

        // Add property name search for active surveys
        if (property_name) {
            getQuery = `
                SELECT * FROM surveys
                WHERE property_name ILIKE $1
                ORDER BY scheduled_time ASC, id ASC
                LIMIT $2 OFFSET $3
            `;
            params = [`%${property_name}%`, PAGE_SIZE, offset];
        } else {
            getQuery = `
                SELECT * FROM surveys
                ORDER BY scheduled_time ASC, id ASC
                LIMIT $1 OFFSET $2
            `;
            params = [PAGE_SIZE, offset];
        }
    } else {
        // Subtract one day from the datetime
        const oneDayBefore = new Date(datetime);
        oneDayBefore.setDate(oneDayBefore.getDate() - 1);
        const adjustedDatetime = oneDayBefore.toISOString();

        if (property_name) {
            getQuery = `
                SELECT * FROM surveys
                WHERE pilot = $1
                AND drone_name = $2
                AND scheduled_time >= $3
                AND property_name ILIKE $4
                ORDER BY scheduled_time ASC, id ASC
                LIMIT 5
            `;
            params = [pilot, drone_name, adjustedDatetime, `%${property_name}%`];
        } else {
            getQuery = `
                SELECT * FROM surveys
                WHERE pilot = $1
                AND drone_name = $2
                AND scheduled_time >= $3
                ORDER BY scheduled_time ASC, id ASC
                LIMIT 5
            `;
            params = [pilot, drone_name, adjustedDatetime];
        }
    }

    const result = await pool.query(getQuery, params);

    return {
        statusCode: 200,
        body: JSON.stringify({ data: result.rows })
    };
}

async function handleGetArchives(event) {
    const { page, property_name } = event.queryStringParameters || {};
    const PAGE_SIZE = 3;
    const pageNumber = parseInt(page, 10) || 1;
    const offset = (pageNumber - 1) * PAGE_SIZE;

    let getQuery;
    let params;

    if (property_name) {
        getQuery = `
            SELECT *, original_id, archived_at FROM archived_surveys
            WHERE property_name ILIKE $1
            ORDER BY archived_at DESC, original_id ASC
            LIMIT $2 OFFSET $3
        `;
        params = [`%${property_name}%`, PAGE_SIZE, offset];
    } else {
        getQuery = `
            SELECT *, original_id, archived_at FROM archived_surveys
            ORDER BY archived_at DESC, original_id ASC
            LIMIT $1 OFFSET $2
        `;
        params = [PAGE_SIZE, offset];
    }

    try {
        const result = await pool.query(getQuery, params);
        return {
            statusCode: 200,
            body: JSON.stringify({ data: result.rows })
        };
    } catch (error) {
        console.error('Error fetching archives:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: 'Error fetching archived surveys', message: error.message })
        };
    }
}

async function handleGetSurveyById(id) {
    const getQuery = `SELECT * FROM surveys WHERE id = $1`;

    try {
        const result = await pool.query(getQuery, [id]);

        if (result.rowCount === 0) {
            return {
                statusCode: 404,
                body: JSON.stringify({ error: 'Survey not found' })
            };
        }

        return {
            statusCode: 200,
            body: JSON.stringify({ data: result.rows[0] })
        };
    } catch (error) {
        console.error('Error fetching survey by ID:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: 'Error fetching survey', message: error.message })
        };
    }
}

async function handlePost(event) {
    const body = JSON.parse(event.body || '{}');
    const {
        pilot,
        drone_name,
        gate_code,
        scheduled_time,
        coordinates,
        notes,
        driving_instructions,
        image_base64,
        google_sheets_id,
        property_name,
        pdf_base64
    } = body;

    if (!pilot || !drone_name || !gate_code || !scheduled_time || !coordinates) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required fields: pilot, drone_name, gate_code, scheduled_time, and coordinates are required'
            })
        };
    }

    const insertQuery = `
        INSERT INTO surveys (
            pilot, drone_name, gate_code, scheduled_time,
            coordinates, notes, driving_instructions,
            image, google_sheets_id, property_name, pdf
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
    `;

    const result = await pool.query(insertQuery, [
        pilot,
        drone_name,
        gate_code,
        scheduled_time,
        coordinates,
        notes || null,
        driving_instructions || null,
        image_base64 ? Buffer.from(image_base64, 'base64') : null,
        google_sheets_id || null,
        property_name || null,
        pdf_base64 ? Buffer.from(pdf_base64, 'base64') : null
    ]);

    return {
        statusCode: 201,
        body: JSON.stringify({
            message: 'Survey added successfully',
            survey: result.rows[0]
        })
    };
}

async function handlePut(event) {
    const body = JSON.parse(event.body || '{}');

    const { id, action } = body;

    // Handle reinstate action
    if (action === 'reinstate') {
        return await handleReinstateSurvey(event);
    }

    if (!id) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required field to identify survey: id is required'
            })
        };
    }

    const updatableFields = [
        'pilot',
        'drone_name',
        'gate_code',
        'scheduled_time',
        'coordinates',
        'notes',
        'driving_instructions',
        'image_base64',
        'google_sheets_id',
        'property_name',
        'pdf_base64'
    ];

    const setClauses = [];
    const values = [];
    let paramIndex = 1;

    for (const field of updatableFields) {
        if (body[field] !== undefined) {
            if (field === 'image_base64') {
                values.push(Buffer.from(body[field], 'base64'));
                setClauses.push(`image = $${paramIndex}`);
            } else if (field === 'pdf_base64') {
                values.push(Buffer.from(body[field], 'base64'));
                setClauses.push(`pdf = $${paramIndex}`);
            } else {
                values.push(body[field]);
                setClauses.push(`${field} = $${paramIndex}`);
            }
            paramIndex++;
        }
    }

    if (setClauses.length === 0) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'No fields provided to update'
            })
        };
    }

    values.push(id);  // For WHERE clause

    const updateQuery = `
        UPDATE surveys
        SET ${setClauses.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
    `;

    try {
        const result = await pool.query(updateQuery, values);

        if (result.rowCount === 0) {
            return {
                statusCode: 404,
                body: JSON.stringify({ error: 'No matching survey found to update' })
            };
        }

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Survey updated successfully',
                survey: result.rows[0]
            })
        };
    } catch (error) {
        console.error('Update error:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                error: 'Internal server error during update',
                message: error.message
            })
        };
    }
}

async function handleDelete(event) {
    const { id, reinstate } = event.queryStringParameters || {};

    // Handle reinstatement from archive
    if (reinstate === 'true') {
        return await handleReinstateSurvey(event);
    }

    if (!id) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required parameter: id is required'
            })
        };
    }

    try {
        // Start transaction
        await pool.query('BEGIN');

        // Get the survey to archive
        const selectQuery = `SELECT * FROM surveys WHERE id = $1`;
        const selectResult = await pool.query(selectQuery, [id]);

        if (selectResult.rowCount === 0) {
            await pool.query('ROLLBACK');
            return {
                statusCode: 404,
                body: JSON.stringify({ error: 'No matching survey found to archive' })
            };
        }

        const survey = selectResult.rows[0];

        // Create archived_surveys table if it doesn't exist
        const createArchiveTableQuery = `
            CREATE TABLE IF NOT EXISTS archived_surveys (
                id SERIAL PRIMARY KEY,
                original_id INTEGER NOT NULL,
                pilot VARCHAR(255),
                drone_name VARCHAR(255),
                gate_code VARCHAR(255),
                scheduled_time TIMESTAMP,
                coordinates TEXT,
                notes TEXT,
                driving_instructions TEXT,
                image BYTEA,
                google_sheets_id VARCHAR(255),
                property_name VARCHAR(255),
                pdf BYTEA,
                archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;
        await pool.query(createArchiveTableQuery);

        // Insert into archive
        const archiveQuery = `
            INSERT INTO archived_surveys (
                original_id, pilot, drone_name, gate_code, scheduled_time,
                coordinates, notes, driving_instructions, image,
                google_sheets_id, property_name, pdf
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        `;

        const archiveResult = await pool.query(archiveQuery, [
            survey.id, survey.pilot, survey.drone_name, survey.gate_code,
            survey.scheduled_time, survey.coordinates, survey.notes,
            survey.driving_instructions, survey.image, survey.google_sheets_id,
            survey.property_name, survey.pdf
        ]);

        // Delete from active surveys
        const deleteQuery = `DELETE FROM surveys WHERE id = $1`;
        await pool.query(deleteQuery, [id]);

        // Commit transaction
        await pool.query('COMMIT');

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Survey archived successfully',
                archived: archiveResult.rows[0]
            })
        };

    } catch (error) {
        await pool.query('ROLLBACK');
        console.error('Error archiving survey:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                error: 'Error archiving survey',
                message: error.message
            })
        };
    }
}

async function handleReinstateSurvey(event) {
    const body = JSON.parse(event.body || '{}');
    const { archive_id, new_scheduled_time } = body;

    if (!archive_id) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required parameter: archive_id is required'
            })
        };
    }

    try {
        // Start transaction
        await pool.query('BEGIN');

        // Get the archived survey
        const selectQuery = `SELECT * FROM archived_surveys WHERE id = $1`;
        const selectResult = await pool.query(selectQuery, [archive_id]);

        if (selectResult.rowCount === 0) {
            await pool.query('ROLLBACK');
            return {
                statusCode: 404,
                body: JSON.stringify({ error: 'No matching archived survey found' })
            };
        }

        const archivedSurvey = selectResult.rows[0];

        // Use new scheduled time if provided, otherwise use original
        const scheduledTime = new_scheduled_time || archivedSurvey.scheduled_time;

        // Insert back into active surveys
        const reinstateQuery = `
            INSERT INTO surveys (
                pilot, drone_name, gate_code, scheduled_time,
                coordinates, notes, driving_instructions, image,
                google_sheets_id, property_name, pdf
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
        `;

        const reinstateResult = await pool.query(reinstateQuery, [
            archivedSurvey.pilot, archivedSurvey.drone_name, archivedSurvey.gate_code,
            scheduledTime, archivedSurvey.coordinates, archivedSurvey.notes,
            archivedSurvey.driving_instructions, archivedSurvey.image,
            archivedSurvey.google_sheets_id, archivedSurvey.property_name, archivedSurvey.pdf
        ]);

        // Delete from archive
        const deleteArchiveQuery = `DELETE FROM archived_surveys WHERE id = $1`;
        await pool.query(deleteArchiveQuery, [archive_id]);

        // Commit transaction
        await pool.query('COMMIT');

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Survey reinstated successfully',
                survey: reinstateResult.rows[0]
            })
        };

    } catch (error) {
        await pool.query('ROLLBACK');
        console.error('Error reinstating survey:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                error: 'Error reinstating survey',
                message: error.message
            })
        };
    }
}
